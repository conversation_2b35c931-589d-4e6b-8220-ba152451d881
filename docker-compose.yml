version: "3.8"

services:
  umbrel:
    image: dockurr/umbrel
    container_name: umbrel
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - umbrel_data:/umbrel
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - umbrel_net
    environment:
      - NEXT_PUBLIC_API_URL=https://umbrel.reizedispatch.com
      - NEXTAUTH_URL=https://umbrel.reizedispatch.com
      - UMBREL_API_URL=https://umbrel.reizedispatch.com
      - UMBREL_FORCE_HTTPS=true
      - UMBREL_PROTOCOL=https
      - DISABLE_CSP=true

  nginx:
    image: nginx:alpine
    container_name: umbrel_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/certs:/etc/letsencrypt
    depends_on:
      - umbrel
    networks:
      - umbrel_net

volumes:
  umbrel_data:

networks:
  umbrel_net:
    driver: bridge
