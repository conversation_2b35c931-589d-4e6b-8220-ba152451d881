version: "3.8"

services:
  umbrel:
    image: dockurr/umbrel
    container_name: umbrel
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - umbrel_data:/umbrel
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - umbrel_net
    environment:
      - UMBREL_DOMAIN=umbrel.reizedispatch.com
      - UMBREL_PROTOCOL=https
      - UMBREL_FORCE_HTTPS=true
      - NEXT_PUBLIC_API_URL=https://umbrel.reizedispatch.com
      - NEXTAUTH_URL=https://umbrel.reizedispatch.com
      - UMBREL_API_URL=https://umbrel.reizedispatch.com
      - DISABLE_CSP=true
      - CSP_DISABLED=true
      - NO_CSP=true
      - SKIP_CSP=true
      - NODE_ENV=production
      - HTTPS=true
      - PORT=80
      - HOST=0.0.0.0
      - UMBREL_URL=https://umbrel.reizedispatch.com
      - PUBLIC_URL=https://umbrel.reizedispatch.com
      - REACT_APP_API_URL=https://umbrel.reizedispatch.com
      - VITE_API_URL=https://umbrel.reizedispatch.com
      - UMBREL_EXTERNAL_URL=https://umbrel.reizedispatch.com
      - UMBREL_BASE_URL=https://umbrel.reizedispatch.com
      - TRPC_URL=https://umbrel.reizedispatch.com/trpc
      - API_BASE_URL=https://umbrel.reizedispatch.com
      - NEXT_PUBLIC_TRPC_URL=https://umbrel.reizedispatch.com/trpc
      - ORIGIN=https://umbrel.reizedispatch.com
      - PROTOCOL_HEADER=x-forwarded-proto
      - HOST_HEADER=x-forwarded-host
      - FORCE_HTTPS=true
      - SECURE=true
      - TRUST_PROXY=true

  nginx:
    image: nginx:alpine
    container_name: umbrel_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/certs:/etc/letsencrypt
    depends_on:
      - umbrel
    networks:
      - umbrel_net

volumes:
  umbrel_data:

networks:
  umbrel_net:
    driver: bridge
