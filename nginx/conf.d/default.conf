# WebSocket connection upgrade mapping
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

server {
    listen 443 ssl;
    server_name umbrel.reizedispatch.com;

    ssl_certificate /etc/letsencrypt/live/umbrel.reizedispatch.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/umbrel.reizedispatch.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # DISABLE ALL SECURITY HEADERS TEMPORARILY
    proxy_hide_header Content-Security-Policy;
    proxy_hide_header X-Content-Security-Policy;
    proxy_hide_header X-WebKit-CSP;

    # Add custom headers to ensure HTTPS is used
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Forwarded-Proto https always;

    # Enable content substitution
    sub_filter_types *;
    sub_filter_once off;

    location / {
        proxy_pass http://umbrel:80;
        proxy_http_version 1.1;

        # WebSocket support - critical for Umbrel HTTPS
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port 443;
        proxy_set_header X-Forwarded-Ssl on;

        # WebSocket specific settings
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;

        proxy_redirect http:// https://;
        proxy_redirect http://$host/ https://$host/;

        # Handle any remaining HTTP redirects and API calls
        sub_filter 'http://umbrel.reizedispatch.com' 'https://umbrel.reizedispatch.com';
        sub_filter '"http://umbrel.reizedispatch.com' '"https://umbrel.reizedispatch.com';
        sub_filter "'http://umbrel.reizedispatch.com" "'https://umbrel.reizedispatch.com";
        sub_filter 'http://localhost' 'https://umbrel.reizedispatch.com';
        sub_filter '"http://localhost' '"https://umbrel.reizedispatch.com';
        sub_filter "'http://localhost" "'https://umbrel.reizedispatch.com";
    }

    location /trpc/ {
        proxy_pass http://umbrel:80/trpc/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port 443;
        proxy_set_header X-Forwarded-Ssl on;
        proxy_set_header Content-Type application/json;
        proxy_buffering off;
        proxy_redirect http:// https://;
        proxy_redirect http://$host/ https://$host/;

        # Handle content substitution for API responses
        sub_filter 'http://umbrel.reizedispatch.com' 'https://umbrel.reizedispatch.com';
        sub_filter '"http://umbrel.reizedispatch.com' '"https://umbrel.reizedispatch.com';
        sub_filter "'http://umbrel.reizedispatch.com" "'https://umbrel.reizedispatch.com";
        sub_filter 'http://localhost' 'https://umbrel.reizedispatch.com';
        sub_filter '"http://localhost' '"https://umbrel.reizedispatch.com';
        sub_filter "'http://localhost" "'https://umbrel.reizedispatch.com";
    }

    # API endpoints
    location /api/ {
        proxy_pass http://umbrel:80/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port 443;
        proxy_set_header X-Forwarded-Ssl on;
        proxy_buffering off;
        proxy_redirect http:// https://;
        proxy_redirect http://$host/ https://$host/;
    }

    # WebSocket support
    location /ws/ {
        proxy_pass http://umbrel:80/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port 443;
        proxy_set_header X-Forwarded-Ssl on;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }
}

server {
    listen 80;
    server_name umbrel.reizedispatch.com;
    return 301 https://$host$request_uri;
}
